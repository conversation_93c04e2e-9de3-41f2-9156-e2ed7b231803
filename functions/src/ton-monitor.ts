import { getHttpEndpoint } from "@orbs-network/ton-access";
import * as admin from "firebase-admin";
import fetch from "node-fetch";
import { getMarketplaceWallet, isDevelopment } from "./config";
import { MIN_TRANSACTION_THRESHOLD_TON } from "./constants";
import { addFunds } from "./services/balance-service";
import { applyDepositFee } from "./services/fee-service";
import { getTxLookup, updateTxLookup } from "./tx-lookup";
import { UserEntity } from "./types";
import { extractRawTonAddress, roundToTwoDecimals } from "./utils";

async function findUserByTonWalletFlexible(
  tonWalletAddress: string
): Promise<UserEntity | null> {
  const db = admin.firestore();
  const usersRef = db.collection("users");

  console.log(`Looking for user with TON address: ${tonWalletAddress}`);

  // First try exact match
  let query = usersRef.where("ton_wallet_address", "==", tonWalletAddress);
  let snapshot = await query.get();

  if (!snapshot.empty) {
    const doc = snapshot.docs[0];
    console.log(`Found exact match for address: ${tonWalletAddress}`);
    return {
      id: doc.id,
      ...doc.data(),
    } as UserEntity;
  }

  const rawAddress = extractRawTonAddress(tonWalletAddress);
  if (!rawAddress) {
    console.log(`Invalid address format: ${tonWalletAddress}`);
    return null;
  }

  console.log(
    `No exact match found, searching by raw address part: ${rawAddress}`
  );

  // Query directly by raw_ton_wallet_address field - much more efficient!
  const rawQuery = usersRef.where("raw_ton_wallet_address", "==", rawAddress);
  const rawSnapshot = await rawQuery.get();

  if (!rawSnapshot.empty) {
    const doc = rawSnapshot.docs[0];
    const userData = doc.data() as UserEntity;
    console.log(
      `Found user by raw address: ${userData.ton_wallet_address} matches ${tonWalletAddress}`
    );
    return {
      ...userData,
      id: doc.id,
    } as UserEntity;
  }

  console.log(`No user found for address: ${tonWalletAddress}`);
  return null;
}

function filterNewTransactions(
  transactions: TonTransaction[],
  lastCheckedLt: string
): TonTransaction[] {
  if (lastCheckedLt === "0") {
    console.log(
      "No previous transactions processed, returning all transactions"
    );
    return transactions;
  }

  const filtered = transactions.filter((tx) => {
    const txLt = parseInt(tx.transaction_id.lt);
    const lastLt = parseInt(lastCheckedLt);
    const isNew = txLt > lastLt;
    console.log(
      `Transaction LT: ${txLt}, Last checked LT: ${lastLt}, Is new: ${isNew}`
    );
    return isNew;
  });

  console.log(
    `Filtered ${transactions.length} transactions down to ${filtered.length} new ones`
  );
  return filtered;
}

interface TonTransaction {
  transaction_id: {
    lt: string;
    hash?: string;
  };
  address: {
    account_address: string;
  };
  utime: number;
  in_msg?: {
    source?: string;
    value: string;
    msg_data?: {
      message?: string;
    };
  };
  out_msgs?: Array<{
    destination?: string;
    value: string;
  }>;
}

async function fetchTonTransactions(
  address: string,
  fromLt?: string,
  limit: number = 100
): Promise<TonTransaction[]> {
  const network = isDevelopment() ? "testnet" : "mainnet";
  const endpoint = await getHttpEndpoint({ network });

  // Use JSON-RPC format for TonCenter API v2
  const requestBody = {
    id: "1",
    jsonrpc: "2.0",
    method: "getTransactions",
    params: {
      address: address,
      limit: limit,
      to_lt: "0",
      archival: true,
      ...(fromLt && fromLt !== "0" && { lt: fromLt }),
    },
  };

  const headers: Record<string, string> = {
    "Content-Type": "application/json",
    Accept: "application/json",
  };

  try {
    console.log(
      `Fetching TON transactions from: ${endpoint} (limit: ${limit}) [${network}]`
    );
    console.log("Request body:", JSON.stringify(requestBody, null, 2));

    const response = await fetch(endpoint, {
      method: "POST",
      headers,
      body: JSON.stringify(requestBody),
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(
        `TON API error: ${response.status} ${response.statusText} - ${errorText}`
      );
    }

    const data = await response.json();

    if (data.error) {
      throw new Error(`TON API returned error: ${JSON.stringify(data.error)}`);
    }

    if (!data.ok || !data.result) {
      throw new Error(
        `TON API returned unsuccessful response: ${JSON.stringify(data)}`
      );
    }

    console.log(`Fetched ${data.result?.length ?? 0} transactions`);
    return data.result ?? [];
  } catch (error) {
    console.error("Error fetching TON transactions:", error);
    throw error;
  }
}

function extractTransactionInfo(
  tx: TonTransaction
): { sender: string; amount: number; message?: string } | null {
  if (!tx?.in_msg?.source || !tx.in_msg.value) {
    return null;
  }

  const amount = roundToTwoDecimals(parseInt(tx.in_msg.value) / 1000000000);
  const originalSender = tx.in_msg.source;

  console.log(
    `Processing transaction from address: ${originalSender}, amount: ${amount} TON`
  );

  return {
    sender: originalSender, // Keep original address, flexible matching will handle it
    amount,
    message: tx.in_msg.msg_data?.message,
  };
}

async function updateUserBalance(
  userId: string,
  amount: number
): Promise<void> {
  try {
    // Apply deposit fee and get net amount
    const netAmount = await applyDepositFee(userId, amount);

    await addFunds(userId, netAmount);
    console.log(
      `Updated balance for user ${userId}: +${netAmount} TON (after fees from ${amount} TON)`
    );
  } catch (error) {
    console.error(`Error updating balance for user ${userId}:`, error);
    throw error;
  }
}

async function processTransactions(
  transactions: TonTransaction[]
): Promise<void> {
  console.log(`Processing ${transactions.length} transactions`);

  for (const tx of transactions) {
    try {
      const txInfo = extractTransactionInfo(tx);

      if (!txInfo) {
        continue;
      }

      console.log(
        `Processing transaction: ${tx.transaction_id.lt}, sender: ${txInfo.sender}, amount: ${txInfo.amount} TON`
      );

      const user = await findUserByTonWalletFlexible(txInfo.sender);

      if (!user) {
        console.log(`No user found for wallet address: ${txInfo.sender}`);
        continue;
      }

      // TODO fix that logic, when I update auth to telegram
      if (!user.tg_id) {
        console.log(`User ${user.id} has no tg_id, skipping balance update`);
        continue;
      }

      await updateUserBalance(user.id, txInfo.amount);

      console.log(
        `Successfully processed topup for user ${user.id} (${user.tg_id}): ${txInfo.amount} TON`
      );
    } catch (error) {
      console.error(
        `Error processing transaction ${tx.transaction_id.lt}:`,
        error
      );
    }
  }
}

export async function monitorTonTransactions(): Promise<void> {
  try {
    console.log("Starting TON transaction monitoring...");

    const txLookup = await getTxLookup();
    const lastCheckedLt = txLookup?.last_checked_record_id ?? "0";

    console.log(`Last checked LT: ${lastCheckedLt}`);

    const marketplaceWallet = getMarketplaceWallet();

    // Fetch transactions in smaller batches to reduce API load
    // Start with a reasonable limit to avoid fetching too many small transactions
    const batchLimit = 50;
    const allTransactions = await fetchTonTransactions(
      marketplaceWallet,
      undefined,
      batchLimit
    );

    if (allTransactions.length === 0) {
      console.log("No transactions found");
      return;
    }

    // Filter out already processed transactions first
    const newTransactions = filterNewTransactions(
      allTransactions,
      lastCheckedLt
    );

    if (newTransactions.length === 0) {
      console.log("No new transactions to process");
      return;
    }

    console.log(`Found ${newTransactions.length} new transactions to process`);

    // Pre-filter transactions by amount > minimum threshold before processing
    const significantTransactions = newTransactions.filter((tx) => {
      if (!tx?.in_msg?.value) return false;
      const amount = parseInt(tx.in_msg.value) / 1000000000;
      return amount > MIN_TRANSACTION_THRESHOLD_TON;
    });

    console.log(
      `Filtered to ${significantTransactions.length} transactions with amount > ${MIN_TRANSACTION_THRESHOLD_TON} TON`
    );

    if (significantTransactions.length === 0) {
      console.log("No significant transactions to process");
      // Still update the LT to mark these transactions as processed
      if (newTransactions.length > 0) {
        const latestLt =
          newTransactions[newTransactions.length - 1].transaction_id.lt;
        await updateTxLookup(latestLt);
        console.log(
          `Updated last checked LT to: ${latestLt} (no significant transactions)`
        );
      }
      return;
    }

    // Sort transactions by LT (oldest first)
    significantTransactions.sort(
      (a, b) => parseInt(a.transaction_id.lt) - parseInt(b.transaction_id.lt)
    );

    await processTransactions(significantTransactions);

    // Update LT based on all new transactions (not just significant ones)
    if (newTransactions.length > 0) {
      const latestLt =
        newTransactions[newTransactions.length - 1].transaction_id.lt;
      await updateTxLookup(latestLt);
      console.log(`Updated last checked LT to: ${latestLt}`);
    }

    console.log("TON transaction monitoring completed successfully");
  } catch (error) {
    console.error("Error in TON transaction monitoring:", error);
    throw error;
  }
}
